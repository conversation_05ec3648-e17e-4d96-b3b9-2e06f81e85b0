<?php

namespace App\Filament\Admin\Resources\SubscriptionResource\RelationManagers;

use App\Constants\TransactionStatus;
use App\Mapper\TransactionStatusMapper;
use App\Models\Receipt;
use App\Models\Transaction;
use App\Services\InvoiceService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Table;
use Filament\Tables\Columns\Layout\Panel;
use Filament\Tables\Columns\Layout\Split;
use Filament\Tables\Columns\Layout\View;
use Illuminate\Database\Eloquent\Builder;

class TransactionsRelationManager extends RelationManager
{
    protected static string $relationship = 'transactions';

    public function form(Form $form): Form
    {
        return $form->schema([]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->heading(__('Invoices & Receipts'))
            ->recordTitleAttribute('invoice_number')
            ->columns([
                Split::make([
                    Tables\Columns\TextColumn::make('invoice_number')
                        ->label(__('Invoice'))
                        ->html()
                        ->searchable(['invoice_number', 'payment_provider_reference'])
                        ->formatStateUsing(function (Transaction $record) {
                            $html = '<div class="space-y-1">';
                            $html .= '<div class="font-medium">' . ($record->invoice_number ?? '-') . '</div>';
                            if ($record->payment_provider_reference) {
                                $html .= '<div class="text-xs text-gray-500 italic">' . $record->payment_provider_reference . '</div>';
                            }
                            $html .= '</div>';
                            return $html;
                        })
                        ->copyable()
                        ->copyableState(fn (Transaction $record) => $record->invoice_number ?? '')
                        ->copyMessage(__('Invoice number copied'))
                        ->copyMessageDuration(1500),

                    Tables\Columns\TextColumn::make('subscription.plan.name')
                        ->label(__('Subscription'))
                        ->searchable()
                        ->sortable(),

                    Tables\Columns\TextColumn::make('amount')
                        ->label(__('Amount'))
                        ->formatStateUsing(function (string $state, Transaction $record) {
                            return money(abs($state), $record->currency->code);
                        })
                        ->sortable(),

                    Tables\Columns\TextColumn::make('status')
                        ->label(__('Status'))
                        ->badge()
                        ->color(fn (Transaction $record, TransactionStatusMapper $mapper): string => $mapper->mapColor($record->status))
                        ->formatStateUsing(fn (string $state, TransactionStatusMapper $mapper): string => $mapper->mapForDisplay($state))
                        ->sortable(),

                    Tables\Columns\TextColumn::make('created_at')
                        ->label(__('Date'))
                        ->dateTime(config('app.datetime_format'))
                        ->sortable()
                        ->searchable(),
                    Tables\Columns\TextColumn::make('uuid')
                        ->label(__('Actions'))
                        ->html()
                        ->formatStateUsing(function (Transaction $record) {
                            // View invoice button
                            $html = 'View Invoice';
                            return $html;
                        })
                        ->action(
                            Action::make('generate_invoice')
                            ->visible(fn (Transaction $record, InvoiceService $invoiceService): bool => $invoiceService->canGenerateInvoices($record))
                            ->modalHeading(__('Customize Invoice Details'))
                            ->modalDescription(__('Please review and customize the invoice details before generating the document.'))
                            ->modalSubmitActionLabel(__('Generate Invoice'))
                            ->form([
                                Forms\Components\Section::make(__('Contact Information'))
                                    ->schema([
                                        Forms\Components\TextInput::make('email')
                                            ->label(__('Email'))
                                            ->email()
                                            ->required()
                                            ->default(fn (Transaction $record) => $record->subscription->user->email),

                                        Forms\Components\TextInput::make('displayed_name')
                                            ->label(__('Displayed Name'))
                                            ->required()
                                            ->default(fn (Transaction $record) => $record->subscription->user->name),

                                        Forms\Components\TextInput::make('phone')
                                            ->label(__('Phone'))
                                            ->tel()
                                            ->default(fn (Transaction $record) => $record->subscription->user->phone_number),
                                    ])
                                    ->columns(2),

                                Forms\Components\Section::make(__('Address Information'))
                                    ->schema([
                                        Forms\Components\TextInput::make('address_line_1')
                                            ->label(__('Address Line 1'))
                                            ->required(),

                                        Forms\Components\TextInput::make('address_line_2')
                                            ->label(__('Address Line 2')),

                                        Forms\Components\TextInput::make('city')
                                            ->label(__('City'))
                                            ->required(),

                                        Forms\Components\TextInput::make('postal_code')
                                            ->label(__('Postal Code'))
                                            ->required(),
                                    ])->columns(2),
                            ])
                            ->action(function (array $data, Transaction $record) {
                                // TODO: Add integration logic here
                                // This will be implemented later
                            }),
                        )
                        ->grow(false)
                        ->alignEnd(),
                ]),

                Panel::make([
                    View::make('filament.admin.transaction-receipts-collapsible')
                        ->components([
                        ])
                ])->collapsible()
                ->visible(fn (Transaction $record): bool => $record->receipts()->count() > 0),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label(__('Status'))
                    ->options([
                        TransactionStatus::SUCCESS->value => __('Success'),
                        TransactionStatus::FAILED->value => __('Failed'),
                        TransactionStatus::PENDING->value => __('Pending'),
                        TransactionStatus::REFUNDED->value => __('Refunded'),
                        TransactionStatus::DISPUTED->value => __('Disputed'),
                    ]),
            ])
            ->headerActions([])
            ->actions([

            ])
            ->bulkActions([])
            ->modifyQueryUsing(fn (Builder $query) => $query->with([
                'currency',
                'paymentProvider',
                'receipts',
                'subscription.plan',
            ]))
            ->defaultSort('created_at', 'desc')
            ->emptyStateHeading(__('No transactions found'))
            ->emptyStateDescription(__('This subscription has no associated transactions yet.'))
            ->emptyStateIcon('heroicon-o-document-text');
    }

    public function generateReceiptAction()
    {
        return \Filament\Actions\Action::make('generate-receipt')
            ->modal()
            ->mountUsing(function (Form $form, array $arguments) {
                // find by receipt uid
                $record = Receipt::where('uuid', $arguments['receipt_uuid'])->firstOrFail();
                $form->fill($record->toArray());
            })
            ->form([
                [
                    Forms\Components\Section::make(__('Contact Information'))
                        ->schema([
                            Forms\Components\TextInput::make('email')
                                ->label(__('Email'))
                                ->email()
                                ->required(),

                            Forms\Components\TextInput::make('displayed_name')
                                ->label(__('Displayed Name'))
                                ->required(),

                            Forms\Components\TextInput::make('phone')
                                ->label(__('Phone'))
                                ->tel(),
                        ])
                        ->columns(2),

                    Forms\Components\Section::make(__('Address Information'))
                        ->schema([
                            Forms\Components\TextInput::make('address_line_1')
                                ->label(__('Address Line 1'))
                                ->required(),

                            Forms\Components\TextInput::make('address_line_2')
                                ->label(__('Address Line 2')),

                            Forms\Components\TextInput::make('city')
                                ->label(__('City'))
                                ->required(),

                            Forms\Components\TextInput::make('postal_code')
                                ->label(__('Postal Code'))
                                ->required(),
                        ])->columns(2),
                ]
            ])
            ->action(function (array $data) {

            });
    }
}
