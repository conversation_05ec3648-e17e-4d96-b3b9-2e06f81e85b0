<div class="p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
    @if($transaction->receipts->count() > 0)
        <div class="mb-3">
            <h4 class="text-sm font-semibold text-gray-900 dark:text-gray-100">
                {{ __('Receipts for Invoice :number', ['number' => $transaction->invoice_number]) }}
            </h4>
        </div>

        <div class="overflow-x-auto">
            <table class="w-full text-sm">
                <thead>
                    <tr class="border-b border-gray-200 dark:border-gray-700">
                        <th class="text-left py-2 px-3 font-semibold text-gray-900 dark:text-gray-100">
                            {{ __('Receipt Number') }}
                        </th>
                        <th class="text-left py-2 px-3 font-semibold text-gray-900 dark:text-gray-100">
                            {{ __('Date') }}
                        </th>
                        <th class="text-left py-2 px-3 font-semibold text-gray-900 dark:text-gray-100">
                            {{ __('Amount') }}
                        </th>
                        <th class="text-left py-2 px-3 font-semibold text-gray-900 dark:text-gray-100">
                            {{ __('Status') }}
                        </th>
                        <th class="text-right py-2 px-3 font-semibold text-gray-900 dark:text-gray-100">
                            {{ __('Actions') }}
                        </th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($transaction->receipts as $receipt)
                        <tr class="border-b border-gray-100 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800/50">
                            <td class="py-2 px-3">
                                <div class="font-medium text-gray-900 dark:text-gray-100">
                                    {{ $receipt->receipt_number }}
                                </div>
                                @if($receipt->payment_provider_object_id)
                                    <div class="text-xs text-gray-500 dark:text-gray-400">
                                        {{ $receipt->payment_provider_object_id }}
                                    </div>
                                @endif
                            </td>
                            <td class="py-2 px-3 text-gray-700 dark:text-gray-300">
                                {{ $receipt->created_at->format(config('app.datetime_format')) }}
                            </td>
                            <td class="py-2 px-3 text-gray-700 dark:text-gray-300">
                                @if($receipt->amount_paid)
                                    {{ money($receipt->amount_paid, $transaction->currency->code) }}
                                @else
                                    {{ money(abs($transaction->amount), $transaction->currency->code) }}
                                @endif
                            </td>
                            <td class="py-2 px-3">
                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium
                                    @if($receipt->receipt_status === 'paid')
                                        bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400
                                    @elseif($receipt->receipt_status === 'pending')
                                        bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400
                                    @elseif($receipt->receipt_status === 'failed')
                                        bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400
                                    @else
                                        bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400
                                    @endif
                                ">
                                    {{ ucfirst($receipt->receipt_status ?? 'unknown') }}
                                </span>
                            </td>
                            <td class="py-2 px-3 text-right">
                                {{ ($this->generateReceiptAction)(['receipt_uuid' => $receipt->uuid]) }}
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    @else
        <div class="text-center py-6">
            <svg class="w-8 h-8 text-gray-400 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            <h3 class="text-sm font-medium text-gray-900 dark:text-gray-100 mb-1">
                {{ __('No receipts found') }}
            </h3>
            <p class="text-xs text-gray-500 dark:text-gray-400">
                {{ __('This transaction has no associated receipts.') }}
            </p>
        </div>
    @endif

    <x-filament-actions::modals />
</div>
